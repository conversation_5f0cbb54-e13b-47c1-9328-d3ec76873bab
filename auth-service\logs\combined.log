{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 17:24:36"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:24:36"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 17:26:05"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:26:05"}
{"error":"password authentication failed for user \"postgres\"","level":"error","message":"Health check failed","service":"auth-service","status":"unhealthy","timestamp":"2025-07-15 17:28:18"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 17:28:50"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:28:50"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:31:26"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 17:31:57"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:31:57"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 17:32:27"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:28"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:46"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:46"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 17:32:58"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:58"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:33:26"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:33:26"}
{"email":"<EMAIL>","error":"password authentication failed for user \"postgres\"","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 17:33:27"}
{"error":"password authentication failed for user \"postgres\"","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"3bd0a84f-2c03-4720-8c3b-f53fc6426413","service":"auth-service","stack":"SequelizeConnectionError: password authentication failed for user \"postgres\"\n    at Client._connectionCallback (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\connection-manager.js:145:24)\n    at Client._handleErrorWhileConnecting (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg\\lib\\client.js:336:19)\n    at Client._handleErrorMessage (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg\\lib\\client.js:356:19)\n    at Connection.emit (node:events:518:28)\n    at D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg\\lib\\connection.js:116:12\n    at Parser.parse (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg-protocol\\dist\\parser.js:36:17)\n    at Socket.<anonymous> (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg-protocol\\dist\\index.js:11:42)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)","timestamp":"2025-07-15 17:33:27","userAgent":"axios/1.10.0"}
{"errors":["Email must be a valid email address","Password must be at least 8 characters long","Password must contain at least one letter and one number"],"level":"warn","message":"Request validation failed","method":"POST","path":"/register","service":"auth-service","timestamp":"2025-07-15 17:33:27"}
{"error":"jwt malformed","level":"warn","message":"JWT token verification failed","service":"auth-service","timestamp":"2025-07-15 17:33:27","tokenType":"JsonWebTokenError"}
{"error":"Invalid token format","ip":"::1","level":"warn","message":"Token verification failed","service":"auth-service","timestamp":"2025-07-15 17:33:27"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 17:34:43"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:34:43"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 18:47:58"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 18:47:58"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"auth-service","timestamp":"2025-07-15 18:48:02"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 19:02:15"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 19:02:16"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 19:03:52"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 19:03:52"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 03:55:31"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 03:55:31"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 03:55:57"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 03:55:57"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 03:57:28"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 03:57:28","tokenBalance":3,"userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 03:57:28","userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 03:57:29","tokenBalance":3,"userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 03:57:29","userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 22:26:57"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 22:26:57"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 22:31:59"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 22:31:59"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-15 22:32:33","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-15 22:32:33","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 22:32:33","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 22:32:33","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:08:19"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"b5a42651-a7c9-452a-bc44-f3254f28bbec","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:08:19","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:08:41"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"cd5dc5d1-b2c6-41b0-9714-ab5590cac3fa","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:08:41","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:08:41","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:08:41","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:10:34"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"e6432726-eb9a-4f2c-b73b-fb407665aa5c","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:10:34","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:10:34","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:10:34","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:11:17"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"bbebde79-15d3-4866-9c4f-f6d844bce977","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:11:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:11:17","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:11:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:13:02"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"91f4529c-1757-4be0-bbaf-aa837095bd42","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:13:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:13:02","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:13:02","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"ip":"::ffff:**********","level":"warn","message":"Internal service authentication failed: Invalid service key","service":"auth-service","timestamp":"2025-07-15 23:13:02","url":"/auth/token-balance"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:13:51"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"92ca9ade-1823-4b90-9b3d-9dca38f46107","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:13:51","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:13:51","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:13:51","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"ip":"::ffff:**********","level":"warn","message":"Internal service authentication failed: Invalid service key","service":"auth-service","timestamp":"2025-07-15 23:13:51","url":"/auth/token-balance"}
{"level":"info","message":"SIGTERM received, shutting down gracefully","service":"auth-service","timestamp":"2025-07-15 23:14:36"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 23:14:38"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 23:14:38"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:14:45"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"0aeb1e9d-7a9b-442b-8295-502a04eaa0fd","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:14:45","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:14:45","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:14:45","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"ip":"::ffff:**********","level":"warn","message":"Internal service authentication failed: Invalid service key","service":"auth-service","timestamp":"2025-07-15 23:14:45","url":"/auth/token-balance"}
{"level":"info","message":"SIGTERM received, shutting down gracefully","service":"auth-service","timestamp":"2025-07-15 23:15:56"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 23:15:58"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 23:15:58"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:16:17"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"c9239e33-6180-4569-8e83-7a9ec1c2b6f9","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:16:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:16:17","tokenBalance":5,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:16:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":4,"oldBalance":4,"operation":"subtract","service":"auth-service","timestamp":"2025-07-15 23:16:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"amount":1,"ip":"::ffff:**********","level":"info","message":"Token balance updated via internal service","newBalance":4,"operation":"subtract","service":"auth-service","timestamp":"2025-07-15 23:16:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:20:58"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"d0a44d01-d8de-491a-9b62-55017c0da149","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:20:58","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:20:59","tokenBalance":4,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:20:59","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":3,"oldBalance":3,"operation":"subtract","service":"auth-service","timestamp":"2025-07-15 23:20:59","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"amount":1,"ip":"::ffff:**********","level":"info","message":"Token balance updated via internal service","newBalance":3,"operation":"subtract","service":"auth-service","timestamp":"2025-07-15 23:20:59","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:27:52"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"612c9f01-812d-4b57-b089-5e71452dd96a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:27:52","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:27:53","tokenBalance":3,"userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:27:53","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-15 23:27:53","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"amount":1,"ip":"::ffff:**********","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-15 23:27:53","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"level":"info","message":"SIGTERM received, shutting down gracefully","service":"auth-service","timestamp":"2025-07-15 23:31:43"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-15 23:32:38"}
{"database":"atma_db","host":"postgres","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-15 23:32:38"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-15 23:32:49","tokenBalance":5,"userId":"69686ed9-2ada-4641-ae9e-81747647701d"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-15 23:32:49","userId":"69686ed9-2ada-4641-ae9e-81747647701d"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:32:49","tokenBalance":5,"userId":"69686ed9-2ada-4641-ae9e-81747647701d"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:32:49","userId":"69686ed9-2ada-4641-ae9e-81747647701d"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:34:09"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"192d655f-d6fa-4cb1-a07f-8cf98cb6ec5b","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:34:09","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:34:09","tokenBalance":5,"userId":"69686ed9-2ada-4641-ae9e-81747647701d"}
{"email":"<EMAIL>","ip":"::ffff:**********","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-15 23:34:09","userId":"69686ed9-2ada-4641-ae9e-81747647701d"}
{"environment":"test","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"errors":["Password is required"],"level":"warn","message":"Request validation failed","method":"POST","path":"/register","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:52:06 +0000] \"POST /auth/register HTTP/1.1\" 400 118 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:52:06 +0000] \"GET /auth/profile HTTP/1.1\" 200 117 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"ip":"::ffff:127.0.0.1","level":"warn","message":"Authentication failed: No token provided","service":"auth-service","timestamp":"2025-07-16 07:52:06","url":"/auth/profile"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:52:06 +0000] \"GET /auth/profile HTTP/1.1\" 401 86 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 07:52:06"}
{"environment":"test","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"email":"<EMAIL>","ip":"::ffff:127.0.0.1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 07:56:26","userId":"user-123"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"POST /auth/register HTTP/1.1\" 201 184 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"error":"Email already exists","ip":"::ffff:127.0.0.1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"fe9ffc0e-5be5-430a-ae54-b439a18f2e55","service":"auth-service","stack":"Error: Email already exists\n    at Object.<anonymous> (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\tests\\auth.test.js:97:50)\n    at Promise.then.completed (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-runner\\build\\runTest.js:444:34)","timestamp":"2025-07-16 07:56:26"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"POST /auth/register HTTP/1.1\" 400 82 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"errors":["Password is required"],"level":"warn","message":"Request validation failed","method":"POST","path":"/register","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"POST /auth/register HTTP/1.1\" 400 118 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"email":"<EMAIL>","ip":"::ffff:127.0.0.1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 07:56:26","userId":"user-123"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"POST /auth/login HTTP/1.1\" 200 172 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"error":"Invalid email or password","ip":"::ffff:127.0.0.1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"5c6d045a-d3a4-4d2e-9715-efa3d18df1e2","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.<anonymous> (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\tests\\auth.test.js:177:47)\n    at Promise.then.completed (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-runner\\build\\runTest.js:444:34)","timestamp":"2025-07-16 07:56:26"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"POST /auth/login HTTP/1.1\" 401 94 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"error":"Cannot read properties of undefined (reading 'id')","ip":"::ffff:127.0.0.1","level":"warn","message":"Authentication failed","service":"auth-service","timestamp":"2025-07-16 07:56:26","url":"/auth/profile"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"GET /auth/profile HTTP/1.1\" 401 86 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"ip":"::ffff:127.0.0.1","level":"warn","message":"Authentication failed: No token provided","service":"auth-service","timestamp":"2025-07-16 07:56:26","url":"/auth/profile"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"GET /auth/profile HTTP/1.1\" 401 86 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"errors":["\"userId\" must be a valid GUID","Amount is required","Operation is required"],"level":"warn","message":"Request validation failed","method":"PUT","path":"/token-balance","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"PUT /auth/token-balance HTTP/1.1\" 400 174 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"ip":"::ffff:127.0.0.1","level":"warn","message":"Internal service authentication failed: Missing internal service header","service":"auth-service","timestamp":"2025-07-16 07:56:26","url":"/auth/token-balance"}
{"level":"info","message":"::ffff:127.0.0.1 - - [16/Jul/2025:00:56:26 +0000] \"PUT /auth/token-balance HTTP/1.1\" 401 94 \"-\" \"-\"","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 07:56:26"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-16 08:17:59"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 08:19:48"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 08:19:48"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 08:21:05","tokenBalance":3,"userId":"209181db-f300-4301-a955-139ca333b703"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 08:21:05","userId":"209181db-f300-4301-a955-139ca333b703"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 08:21:05","tokenBalance":3,"userId":"209181db-f300-4301-a955-139ca333b703"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 08:21:05","userId":"209181db-f300-4301-a955-139ca333b703"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 08:40:15","tokenBalance":3,"userId":"77596f49-a72e-449f-ba71-e40da23d09c4"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 08:40:15","userId":"77596f49-a72e-449f-ba71-e40da23d09c4"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 13:16:18"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 13:16:18"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 13:18:04"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 13:18:04"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 13:18:47","tokenBalance":3,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 13:18:47","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 13:19:05","tokenBalance":3,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 13:19:05","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 13:19:12","tokenBalance":3,"userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 13:19:12","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 13:20:59"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 13:20:59"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 13:26:05"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 13:26:05"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 14:41:41"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 14:41:41"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 14:43:01"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 14:43:01"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 14:43:01"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 14:43:01"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 14:43:08"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 14:43:08"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 15:05:24"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:05:24"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:30"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:30"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:30"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:30"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:52"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:52"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:55"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:55"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:56"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:56"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:56"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:56"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:56"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 15:20:56"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 15:58:03","tokenBalance":3,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 15:58:03","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-16 15:58:15"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"6720fadf-736c-430b-8e35-f0e466e5cf9e","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-16 15:58:15","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 15:58:27","tokenBalance":3,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 15:58:27","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 15:58:31","tokenBalance":3,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 15:58:31","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:15:14"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:15:14"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:15:14"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:15:14"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:15:14"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:15:17"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"84d3593c-9cca-4f53-b7b5-6520b0290890","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:15:17","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 16:15:30","tokenBalance":3,"userId":"b0f4c98f-f14c-4364-8d5f-c53f0f2acf1e"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 16:15:30","userId":"b0f4c98f-f14c-4364-8d5f-c53f0f2acf1e"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:15:50"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"3dcace34-f05e-4a7e-996b-8fa371059b56","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:15:50","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:15:53"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"23247aa0-b791-40cc-a778-ee9c583f69f7","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:15:53","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:18:32"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"bfaaaab6-c7ae-45d7-845b-31d2a4514395","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:18:32","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:18:36"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"2040f2b4-99c4-42f0-9f0d-aedd02646eba","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:18:36","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:18:49"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"ee122e38-7816-40f7-a693-8e3d47486bf4","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:18:49","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:24:42"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:24:42"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:24:43"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"e3c3b757-d658-4824-a6d6-5f02c0d3ec4f","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:24:43","userAgent":"axios/1.10.0"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:25:24"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:25:24"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 16:25:24","tokenBalance":3,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 16:25:24","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:28:11"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:28:11"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 16:28:12","tokenBalance":3,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 16:28:12","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:30:43"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:30:43"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 16:30:43","tokenBalance":3,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 16:30:43","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-16 16:30:44","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-16 16:30:44","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 16:33:50","tokenBalance":2,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 16:33:50","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 16:33:51","tokenBalance":3,"userId":"3036cd79-b8bb-427c-8015-dda9b4cbf7f8"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 16:33:51","userId":"3036cd79-b8bb-427c-8015-dda9b4cbf7f8"}
{"email":"<EMAIL>","level":"info","message":"User profile updated successfully","service":"auth-service","timestamp":"2025-07-16 16:33:53","updatedFields":[],"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User profile updated","service":"auth-service","timestamp":"2025-07-16 16:33:53","updatedFields":[],"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-16 16:33:55","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-16 16:33:55","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:34:02"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:34:02"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:34:02"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:34:02"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 16:34:02"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 18:58:42","tokenBalance":1,"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-16 18:58:42","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 18:58:44","tokenBalance":3,"userId":"0f50959d-c221-4360-97f4-7a45c8c19678"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-16 18:58:44","userId":"0f50959d-c221-4360-97f4-7a45c8c19678"}
{"email":"<EMAIL>","level":"info","message":"User profile updated successfully","service":"auth-service","timestamp":"2025-07-16 18:58:46","updatedFields":[],"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User profile updated","service":"auth-service","timestamp":"2025-07-16 18:58:46","updatedFields":[],"userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":0,"oldBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-16 18:58:48","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-16 18:58:48","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 18:58:54"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 18:58:54"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 18:58:54"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 18:58:54"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 18:58:54"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 23:26:06"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 23:26:06"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-16 23:26:46"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-16 23:26:46"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-17 05:44:46"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 05:44:46"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 05:45:54","tokenBalance":3,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 05:45:54","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 06:00:12"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 06:00:13"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 06:00:13"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 06:00:13"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 06:00:14"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 06:00:14"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-17 06:00:23"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"0404b631-1e62-4c16-87ce-5ee22f30db72","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-17 06:00:23","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 06:00:33","tokenBalance":3,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 06:00:33","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-17 06:00:41","tokenBalance":3,"userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-17 06:00:41","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-17 07:42:16","userId":"c71c63e3-929c-4a6b-9bab-09ce1abdfaa1"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-17 07:42:22","tokenBalance":3,"userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-17 07:42:22","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 08:01:55","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 08:01:55","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-17 08:31:32"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 08:31:33"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-17 10:04:33"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 10:04:33"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-17 10:42:59"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 10:42:59"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-17 11:12:34"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 11:12:35"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-17 11:40:23"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 11:40:23"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-17 11:40:36","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 11:41:15","tokenBalance":3,"userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 11:41:15","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-17 12:05:08"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 12:05:08"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 12:13:16","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 12:13:16","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-17 12:22:14"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 12:22:14"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 12:22:48","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 12:22:48","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":0,"oldBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 12:31:22","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 12:31:22","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-17 18:15:30"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 18:15:30"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 18:21:48","tokenBalance":3,"userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 18:21:48","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 18:23:12","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 18:23:12","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 18:26:39","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 18:26:39","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-17 19:31:12","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-17 19:31:23","tokenBalance":1,"userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-17 19:31:23","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":0,"oldBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 19:31:29","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 19:31:29","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-17 19:32:44","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 19:33:23","tokenBalance":3,"userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 19:33:23","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 19:34:32","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 19:34:32","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:27:31","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:27:31","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":0,"oldBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:28:33","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:28:33","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-17 20:32:38","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 20:32:56","tokenBalance":3,"userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 20:32:56","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:33:46","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:33:46","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 20:36:15"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 20:36:15"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:38:31","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:38:31","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-17 20:46:28"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 20:46:29"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":0,"oldBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:46:59","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:46:59","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-17 20:47:42","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-17 20:55:52"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-17 20:55:52"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-17 20:56:31","tokenBalance":0,"userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-17 20:56:31","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-17 20:57:18","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 20:57:40","tokenBalance":3,"userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-17 20:57:40","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:58:39","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 20:58:39","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 21:06:48","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 21:06:48","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":0,"oldBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 21:07:18","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-17 21:07:18","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 03:53:12"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 03:53:13"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 03:53:27","tokenBalance":0,"userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 03:53:27","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-18 03:53:30","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"email":"<EMAIL>","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 03:53:42","tokenBalance":3,"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User registered successfully","service":"auth-service","timestamp":"2025-07-18 03:53:42","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":2,"oldBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 03:53:50","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":2,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 03:53:50","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":1,"oldBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 03:55:38","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":1,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 03:55:38","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":0,"oldBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 03:56:19","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":0,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 03:56:19","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-18 04:23:13","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 04:23:17","tokenBalance":0,"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 04:23:17","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-18 04:23:27","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 05:34:50"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 05:34:50"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 05:35:15"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 05:35:15"}
{"error":"Invalid value { username: 'superadmin' }","level":"error","message":"Admin login failed","service":"auth-service","timestamp":"2025-07-18 05:35:39","username":"superadmin"}
{"error":"Invalid value { username: 'superadmin' }","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","requestId":"59f20432-3a02-4c8e-9e89-53b9d9dd2f13","service":"auth-service","stack":"Error: Invalid value { username: 'superadmin' }\n    at Object.escape (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\sql-string.js:54:11)\n    at PostgresQueryGenerator.escape (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:766:22)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1996:71\n    at Array.map (<anonymous>)\n    at PostgresQueryGenerator._whereParseSingleValueObject (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1996:52)\n    at PostgresQueryGenerator.whereItemQuery (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1812:19)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1737:25\n    at Array.forEach (<anonymous>)\n    at PostgresQueryGenerator.whereItemsQuery (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1735:35)\n    at PostgresQueryGenerator.getWhereConditions (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:2078:19)","timestamp":"2025-07-18 05:35:39","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Invalid value { username: 'superadmin' }","level":"error","message":"Admin login failed","service":"auth-service","timestamp":"2025-07-18 05:36:04","username":"superadmin"}
{"error":"Invalid value { username: 'superadmin' }","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","requestId":"e1bf6768-182f-4b28-a069-e388f3e25203","service":"auth-service","stack":"Error: Invalid value { username: 'superadmin' }\n    at Object.escape (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\sql-string.js:54:11)\n    at PostgresQueryGenerator.escape (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:766:22)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1996:71\n    at Array.map (<anonymous>)\n    at PostgresQueryGenerator._whereParseSingleValueObject (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1996:52)\n    at PostgresQueryGenerator.whereItemQuery (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1812:19)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1737:25\n    at Array.forEach (<anonymous>)\n    at PostgresQueryGenerator.whereItemsQuery (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1735:35)\n    at PostgresQueryGenerator.getWhereConditions (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:2078:19)","timestamp":"2025-07-18 05:36:04","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 05:38:45"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 05:38:45"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 05:39:45"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 05:39:45"}
{"error":"Invalid username/email or password","level":"error","message":"Admin login failed","service":"auth-service","timestamp":"2025-07-18 05:39:59","username":"superadmin"}
{"error":"Invalid username/email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","requestId":"4953c00f-1f82-4b2d-95ac-4856292b29e1","service":"auth-service","stack":"Error: Invalid username/email or password\n    at Object.loginAdmin (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\adminService.js:126:13)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\adminController.js:65:20)","timestamp":"2025-07-18 05:39:59","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 05:46:36"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 05:46:57"}
{"error":"request aborted","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","service":"auth-service","stack":"BadRequestError: request aborted\n    at IncomingMessage.onAborted (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\raw-body\\index.js:245:10)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage._destroy (node:_http_incoming:221:10)\n    at _destroy (node:internal/streams/destroy:122:10)\n    at IncomingMessage.destroy (node:internal/streams/destroy:84:5)\n    at abortIncoming (node:_http_server:811:9)\n    at socketOnClose (node:_http_server:805:3)\n    at Socket.emit (node:events:530:35)\n    at TCP.<anonymous> (node:net:346:12)","timestamp":"2025-07-18 05:47:41","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 05:47:41"}
{"error":"Invalid username/email or password","level":"error","message":"Admin login failed","service":"auth-service","timestamp":"2025-07-18 05:47:48","username":"superadmin"}
{"error":"Invalid username/email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","requestId":"3a1e19ea-5604-462e-96fe-c51aed2ca653","service":"auth-service","stack":"Error: Invalid username/email or password\n    at Object.loginAdmin (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\adminService.js:126:13)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\adminController.js:65:20)","timestamp":"2025-07-18 05:47:48","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 05:48:36"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 05:48:36"}
{"error":"Invalid username/email or password","level":"error","message":"Admin login failed","service":"auth-service","timestamp":"2025-07-18 05:49:10","username":"superadmin"}
{"error":"Invalid username/email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","requestId":"0ef7e33c-665f-473e-b697-e6ec0fed6167","service":"auth-service","stack":"Error: Invalid username/email or password\n    at Object.loginAdmin (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\adminService.js:126:13)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\adminController.js:65:20)","timestamp":"2025-07-18 05:49:10","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 05:52:32"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 05:52:54"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 05:53:19","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 05:53:19","username":"superadmin"}
{"error":"Invalid username/email or password","level":"error","message":"Admin login failed","service":"auth-service","timestamp":"2025-07-18 05:53:45","username":"superadmin"}
{"error":"Invalid username/email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","requestId":"a0a820b1-effb-426f-8500-c5d8de9a0f1f","service":"auth-service","stack":"Error: Invalid username/email or password\n    at Object.loginAdmin (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\adminService.js:126:13)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\adminController.js:65:20)","timestamp":"2025-07-18 05:53:45","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 05:54:13","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 05:54:13","username":"superadmin"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 05:55:25"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 05:55:25"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 05:58:52","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 05:58:52","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 05:59:27","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 05:59:27","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 06:00:33","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 06:00:33","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 06:07:20","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 06:07:20","username":"superadmin"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 06:17:30"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 06:17:31"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 06:17:54","tokenBalance":0,"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 06:17:54","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 06:18:04","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 06:18:04","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","ip":"::1","level":"info","message":"Admin logged out","service":"auth-service","timestamp":"2025-07-18 06:18:04","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","ip":"::1","level":"info","message":"Admin logged out","service":"auth-service","timestamp":"2025-07-18 06:18:04","username":"superadmin"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 06:18:33"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 06:18:33"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 06:21:50"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 06:21:50"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 06:25:17","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 06:25:17","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 06:43:22","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 06:43:22","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 07:03:51","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 07:03:51","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 07:04:00","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 07:04:00","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 07:06:26","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 07:06:26","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 07:06:34","username":"superadmin"}
{"adminId":"cfb5baa4-98f9-4893-b680-05a91e5d0496","email":"<EMAIL>","ip":"::1","level":"info","message":"Admin login successful","role":"superadmin","service":"auth-service","timestamp":"2025-07-18 07:06:34","username":"superadmin"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 07:20:28","tokenBalance":20,"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 07:20:28","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":19,"oldBalance":19,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 07:20:31","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":19,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 07:20:31","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":18,"oldBalance":18,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 07:29:40","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":18,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 07:29:40","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":17,"oldBalance":17,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 07:34:16","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":17,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 07:34:16","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":16,"oldBalance":16,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 07:40:10","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":16,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 07:40:10","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 08:02:36"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 08:02:36"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 08:10:37"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 08:10:37"}
{"environment":"development","level":"info","message":"Auth Service started on port 3001","port":"3001","service":"auth-service","timestamp":"2025-07-18 08:21:02"}
{"database":"atma_db","host":"localhost","level":"info","message":"Database connection established successfully","schema":"auth","service":"auth-service","timestamp":"2025-07-18 08:21:02"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":15,"oldBalance":15,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:21:48","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":15,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:21:48","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":14,"oldBalance":14,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:22:13","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":14,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:22:13","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":13,"oldBalance":13,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:23:31","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":13,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:23:31","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":12,"oldBalance":12,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:24:00","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":12,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:24:00","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":11,"oldBalance":11,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:32:37","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":11,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:32:37","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"email":"<EMAIL>","level":"info","message":"Token balance updated successfully","newBalance":10,"oldBalance":10,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:38:34","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"amount":1,"ip":"::1","level":"info","message":"Token balance updated via internal service","newBalance":10,"operation":"subtract","service":"auth-service","timestamp":"2025-07-18 08:38:34","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-18 08:40:32","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 08:40:34","tokenBalance":10,"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 08:40:34","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"ip":"::1","level":"info","message":"User logged out","service":"auth-service","timestamp":"2025-07-18 08:46:07","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 08:46:09","tokenBalance":10,"userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"email":"<EMAIL>","ip":"::1","level":"info","message":"User login successful","service":"auth-service","timestamp":"2025-07-18 08:46:09","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
