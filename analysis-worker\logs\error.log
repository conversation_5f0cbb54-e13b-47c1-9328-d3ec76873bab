{"errors":["\"assessmentData.multipleIntelligences\" is required","\"assessmentData.cognitiveStyleIndex\" is required"],"jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:28:28","version":"1.0.0"}
{"error":"Invalid message format: \"assessmentData.multipleIntelligences\" is required, \"assessmentData.cognitiveStyleIndex\" is required","jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"error","message":"Failed to process assessment job","processingTime":"5ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 10:28:28","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"95024221-f5cf-438a-9c0b-f8088f3dc608","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:28:28","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"95024221-f5cf-438a-9c0b-f8088f3dc608","level":"error","message":"Failed to process assessment job","processingTime":"2ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 10:28:28","userId":"test-user-1752716180082","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"03598308-041c-4180-89ea-f973bde1d5ce","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:28:28","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"03598308-041c-4180-89ea-f973bde1d5ce","level":"error","message":"Failed to process assessment job","processingTime":"1ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 10:28:28","userId":"test-user-1752716907720","version":"1.0.0"}
{"errors":["\"assessmentData.multipleIntelligences\" is required","\"assessmentData.cognitiveStyleIndex\" is required"],"jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:28:33","version":"1.0.0"}
{"error":"Invalid message format: \"assessmentData.multipleIntelligences\" is required, \"assessmentData.cognitiveStyleIndex\" is required","jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"error","message":"Failed to process assessment job","processingTime":"1ms","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:28:33","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"95024221-f5cf-438a-9c0b-f8088f3dc608","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:28:33","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"95024221-f5cf-438a-9c0b-f8088f3dc608","level":"error","message":"Failed to process assessment job","processingTime":"0ms","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:28:33","userId":"test-user-1752716180082","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"03598308-041c-4180-89ea-f973bde1d5ce","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:28:33","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"03598308-041c-4180-89ea-f973bde1d5ce","level":"error","message":"Failed to process assessment job","processingTime":"1ms","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:28:33","userId":"test-user-1752716907720","version":"1.0.0"}
{"errors":["\"assessmentData.multipleIntelligences\" is required","\"assessmentData.cognitiveStyleIndex\" is required"],"jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:28:43","version":"1.0.0"}
{"error":"Invalid message format: \"assessmentData.multipleIntelligences\" is required, \"assessmentData.cognitiveStyleIndex\" is required","jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"error","message":"Failed to process assessment job","processingTime":"1ms","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:28:43","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"95024221-f5cf-438a-9c0b-f8088f3dc608","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:28:43","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"95024221-f5cf-438a-9c0b-f8088f3dc608","level":"error","message":"Failed to process assessment job","processingTime":"0ms","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:28:43","userId":"test-user-1752716180082","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"03598308-041c-4180-89ea-f973bde1d5ce","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:28:43","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"03598308-041c-4180-89ea-f973bde1d5ce","level":"error","message":"Failed to process assessment job","processingTime":"1ms","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:28:43","userId":"test-user-1752716907720","version":"1.0.0"}
{"errors":["\"assessmentData.multipleIntelligences\" is required","\"assessmentData.cognitiveStyleIndex\" is required"],"jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:29:03","version":"1.0.0"}
{"error":"Invalid message format: \"assessmentData.multipleIntelligences\" is required, \"assessmentData.cognitiveStyleIndex\" is required","jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"error","message":"Failed to process assessment job","processingTime":"1ms","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:29:03","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63","version":"1.0.0"}
{"error":"Invalid message format: \"assessmentData.multipleIntelligences\" is required, \"assessmentData.cognitiveStyleIndex\" is required","jobId":"b3108b24-edbc-4f93-b405-f9c556eb51d3","level":"error","maxRetries":3,"message":"Max retries exceeded, sending to dead letter queue","retryCount":4,"service":"analysis-worker","timestamp":"2025-07-17 10:29:03","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"95024221-f5cf-438a-9c0b-f8088f3dc608","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:29:03","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"95024221-f5cf-438a-9c0b-f8088f3dc608","level":"error","message":"Failed to process assessment job","processingTime":"1ms","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:29:03","userId":"test-user-1752716180082","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"95024221-f5cf-438a-9c0b-f8088f3dc608","level":"error","maxRetries":3,"message":"Max retries exceeded, sending to dead letter queue","retryCount":4,"service":"analysis-worker","timestamp":"2025-07-17 10:29:03","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"03598308-041c-4180-89ea-f973bde1d5ce","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:29:03","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"03598308-041c-4180-89ea-f973bde1d5ce","level":"error","message":"Failed to process assessment job","processingTime":"0ms","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:29:03","userId":"test-user-1752716907720","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"03598308-041c-4180-89ea-f973bde1d5ce","level":"error","maxRetries":3,"message":"Max retries exceeded, sending to dead letter queue","retryCount":4,"service":"analysis-worker","timestamp":"2025-07-17 10:29:03","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:31:57","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"error","message":"Failed to process assessment job","processingTime":"5ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 10:31:57","userId":"test-user-1752723117024","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:32:02","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"error","message":"Failed to process assessment job","processingTime":"2ms","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:32:02","userId":"test-user-1752723117024","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:32:12","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"error","message":"Failed to process assessment job","processingTime":"1ms","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:32:12","userId":"test-user-1752723117024","version":"1.0.0"}
{"errors":["\"userId\" must be a valid GUID"],"jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"error","message":"Job message validation failed","service":"analysis-worker","timestamp":"2025-07-17 10:32:32","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"error","message":"Failed to process assessment job","processingTime":"2ms","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:32:32","userId":"test-user-1752723117024","version":"1.0.0"}
{"error":"Invalid message format: \"userId\" must be a valid GUID","jobId":"19cb1e75-f1a9-4e6c-9242-05038ad0fc58","level":"error","maxRetries":3,"message":"Max retries exceeded, sending to dead letter queue","retryCount":4,"service":"analysis-worker","timestamp":"2025-07-17 10:32:32","version":"1.0.0"}
{"error":"exception TypeError: fetch failed sending request","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to generate persona profile","service":"analysis-worker","timestamp":"2025-07-17 10:33:54","version":"1.0.0"}
{"error":"exception TypeError: fetch failed sending request","level":"error","maxRetries":3,"message":"All retry attempts failed for AI persona generation","service":"analysis-worker","timestamp":"2025-07-17 10:33:54","version":"1.0.0"}
{"error":"exception TypeError: fetch failed sending request","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Assessment processing failed","service":"analysis-worker","stack":"Error: exception TypeError: fetch failed sending request\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\analysis-worker\\node_modules\\@google\\genai\\dist\\node\\index.js:6178:19\n    at async Models.generateContent (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\analysis-worker\\node_modules\\@google\\genai\\dist\\node\\index.js:2874:20)\n    at async Object.generatePersonaProfile (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\analysis-worker\\src\\services\\aiService.js:198:22)\n    at async withRetry (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\analysis-worker\\src\\utils\\errorHandler.js:134:14)\n    at async D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\analysis-worker\\src\\processors\\assessmentProcessor.js:32:30","timestamp":"2025-07-17 10:33:54","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 10:33:54","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Assessment processing failed: exception TypeError: fetch failed sending request","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to process assessment job","processingTime":"6237ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 10:33:54","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-17 10:34:18","url":"/archive/results","version":"1.0.0"}
{"error":"","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-17 10:34:18","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-17 10:34:23","url":"/archive/results","version":"1.0.0"}
{"error":"","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-17 10:34:23","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-17 10:34:33","url":"/archive/results","version":"1.0.0"}
{"error":"","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-17 10:34:33","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-17 10:34:53","url":"/archive/results","version":"1.0.0"}
{"error":"","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-17 10:34:53","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 10:34:53","version":"1.0.0"}
{"error":"","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 10:34:53","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 10:34:53","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Assessment processing failed: ","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to process assessment job","processingTime":"53179ms","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 10:34:53","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:35:25","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:35:25","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:35:30","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:35:30","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:35:40","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:35:40","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:36:00","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:36:00","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 10:36:00","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 10:36:00","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 10:36:00","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to process assessment job","processingTime":"57101ms","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 10:36:00","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:36:40","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:36:40","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:36:45","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:36:45","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:36:55","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:36:55","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:37:15","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 10:37:15","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 10:37:15","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 10:37:15","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 10:37:15","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","message":"Failed to process assessment job","processingTime":"54924ms","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 10:37:15","userId":"a976e5bc-2074-42de-854b-84f52293233f","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"807bee98-0b28-4fa9-be52-0a3f9161a672","level":"error","maxRetries":3,"message":"Max retries exceeded, sending to dead letter queue","retryCount":4,"service":"analysis-worker","timestamp":"2025-07-17 10:37:15","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:03:05","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:03:05","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:03:10","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:03:10","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:03:20","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:03:20","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:03:40","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:03:40","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:03:40","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 11:03:40","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 11:03:40","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to process assessment job","processingTime":"60900ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 11:03:40","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:04:05","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:04:05","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:04:10","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:04:10","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:13:57","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:13:57","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:02","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:02","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:12","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:12","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:32","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:32","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:14:32","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 11:14:32","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 11:14:32","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to process assessment job","processingTime":"54347ms","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 11:14:32","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:41","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:41","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:46","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:46","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:56","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:14:56","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:00","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:00","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:05","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:05","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:15","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:15","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-17 11:15:16","url":"/archive/results","version":"1.0.0"}
{"error":"","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-17 11:15:16","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:15:16","version":"1.0.0"}
{"error":"","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 11:15:16","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 11:15:16","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Assessment processing failed: ","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to process assessment job","processingTime":"53969ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 11:15:16","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:35","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:35","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:15:35","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 11:15:35","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 11:15:35","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to process assessment job","processingTime":"52640ms","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 11:15:35","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:39","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:39","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:44","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:44","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:54","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:15:54","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:14","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:14","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:16:14","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 11:16:14","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:14","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:14","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 11:16:14","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to process assessment job","processingTime":"52595ms","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 11:16:14","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:19","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:19","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:29","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:29","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:42","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:42","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:47","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:47","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:49","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:49","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:16:49","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 11:16:49","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 11:16:49","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","message":"Failed to process assessment job","processingTime":"54381ms","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 11:16:49","userId":"e02e6de5-228c-4c4e-bfbf-578260eeeb18","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"50cc3e3c-ac30-496f-b9e5-4e7f1eb55cdf","level":"error","maxRetries":3,"message":"Max retries exceeded, sending to dead letter queue","retryCount":4,"service":"analysis-worker","timestamp":"2025-07-17 11:16:49","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:57","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:16:57","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"","level":"error","message":"Archive service response error","service":"analysis-worker","timestamp":"2025-07-17 11:17:17","url":"/archive/results","version":"1.0.0"}
{"error":"","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","timestamp":"2025-07-17 11:17:17","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:17:17","version":"1.0.0"}
{"error":"","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 11:17:17","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 11:17:17","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Assessment processing failed: ","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to process assessment job","processingTime":"53210ms","retryCount":2,"service":"analysis-worker","timestamp":"2025-07-17 11:17:17","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:17:57","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:17:57","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:18:02","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:18:02","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:18:12","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:18:12","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:18:32","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:18:32","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:18:32","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 11:18:32","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 11:18:32","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","message":"Failed to process assessment job","processingTime":"55164ms","retryCount":3,"service":"analysis-worker","timestamp":"2025-07-17 11:18:32","userId":"90d1fb77-9dd1-4320-a098-a355cfb05b61","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"bd143df5-5a7d-4dfa-ae5e-dc89659a6d86","level":"error","maxRetries":3,"message":"Max retries exceeded, sending to dead letter queue","retryCount":4,"service":"analysis-worker","timestamp":"2025-07-17 11:18:32","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:28:10","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:28:10","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:28:15","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:28:15","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:28:25","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:28:25","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:28:45","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:28:45","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:28:45","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 11:28:45","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 11:28:45","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to process assessment job","processingTime":"52412ms","retryCount":0,"service":"analysis-worker","timestamp":"2025-07-17 11:28:45","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:29:09","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:29:09","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:29:14","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:29:14","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:29:24","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:29:24","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:29:44","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:29:44","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","maxRetries":3,"message":"All retry attempts failed for Archive service save","service":"analysis-worker","timestamp":"2025-07-17 11:29:44","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Assessment processing failed","service":"analysis-worker","timestamp":"2025-07-17 11:29:44","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to send analysis failure notification","service":"analysis-worker","timestamp":"2025-07-17 11:29:44","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Assessment processing failed: Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to process assessment job","processingTime":"53945ms","retryCount":1,"service":"analysis-worker","timestamp":"2025-07-17 11:29:44","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:30:13","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:30:13","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 500","level":"error","message":"Archive service response error","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:30:18","url":"/archive/results","version":"1.0.0"}
{"error":"Request failed with status code 500","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to save analysis result","service":"analysis-worker","status":500,"statusText":"Internal Server Error","timestamp":"2025-07-17 11:30:18","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"64814b5e-657e-4f8c-85a9-0e8b09320574","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-17 11:30:28","userId":"e9034651-8aa9-471b-97c7-71babc1ac589","version":"1.0.0"}
{"error":"Request failed with status code 401","jobId":"3797105b-a4ac-436d-b2c8-9d812d06f52d","level":"error","message":"Failed to send analysis complete notification","service":"analysis-worker","timestamp":"2025-07-17 12:13:33","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65","version":"1.0.0"}
{"errors":["\"weaknesses\" must contain at least 3 items"],"level":"error","message":"Persona profile validation failed","service":"analysis-worker","timestamp":"2025-07-18 09:49:48","version":"1.0.0"}
{"error":"Channel closed by server: 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'durable' for exchange 'atma_exchange' in vhost '/': received 'false' but current is 'true'\"","level":"error","message":"RabbitMQ connection error","service":"analysis-worker","timestamp":"2025-07-18 09:51:32","version":"1.0.0"}
